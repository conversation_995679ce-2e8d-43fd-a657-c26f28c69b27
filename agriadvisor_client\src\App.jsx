// src/App.jsx

import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import useAuth from "./hooks/useAuth";

// Import the spinner for loading state
import FullScreenSpinner from "./components/FullScreenSpinner";

// Pages and Layouts
import LoginPage from "./pages/LoginPage";
import RegistrationPage from "./pages/RegistrationPage";
import AdminLayout from "./layouts/AdminLayout";
import DashboardPage from "./pages/DashboardPage";
import ExpertsPage from "./pages/ExpertsPage";
import ServicesPage from "./pages/ServicesPage";
import BookingsPage from "./pages/BookingsPage";
import FarmersPage from "./pages/FarmersPage";

// The ProtectedRoute logic is now simpler and optimized
const ProtectedRoute = React.memo(() => {
  const { user } = useAuth();

  // If no user, redirect immediately without any delay
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If user exists, render the admin layout
  return <AdminLayout />;
});

function App() {
  const { user, loading } = useAuth();

  // Show loading spinner only briefly during initial auth check
  if (loading) {
    return <FullScreenSpinner />;
  }

  return (
    <Router>
      <div style={{ minHeight: "100vh" }}>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={!user ? <LoginPage /> : <Navigate to="/" replace />} />
          <Route path="/register" element={!user ? <RegistrationPage /> : <Navigate to="/" replace />} />

          {/* Protected Routes for the Admin Dashboard */}
          <Route element={<ProtectedRoute />}>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/experts" element={<ExpertsPage />} />
            <Route path="/services" element={<ServicesPage />} />
            <Route path="/bookings" element={<BookingsPage />} />
            <Route path="/farmers" element={<FarmersPage />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
}

export default App;


