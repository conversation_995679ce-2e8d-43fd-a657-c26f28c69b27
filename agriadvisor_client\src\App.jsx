// src/App.jsx

import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import useAuth from "./hooks/useAuth";

// --- Layouts ---
import AdminLayout from "./layouts/AdminLayout";
import FarmerLayout from "./layouts/FarmerLayout";

// --- Public Pages ---
import LoginPage from "./pages/LoginPage";
import RegistrationPage from "./pages/RegistrationPage";

// --- Admin Pages ---
import DashboardPage from "./pages/DashboardPage";
import ServicesPage from "./pages/ServicesPage";
import ExpertsPage from "./pages/ExpertsPage";
import BookingsPage from "./pages/BookingsPage";
import FarmersPage from "./pages/FarmersPage";

// --- Farmer Pages ---
import FarmerDashboardPage from "./pages/FarmerDashboardPage";
import BrowseServicesPage from "./pages/BrowseServicesPage";
import FarmerBookingsPage from "./pages/FarmerBookingsPage";

/**
 * A component to protect routes for the Admin role.
 * If the user is logged in AND is an admin, it renders the AdminLayout.
 * Otherwise, it navigates them away.
 */
const AdminRoutes = () => {
  const { user } = useAuth();
  if (!user) return <Navigate to="/login" />;
  if (user.role !== "admin") return <Navigate to="/" />; // Or a "Not Authorized" page

  return <AdminLayout />;
};

/**
 * A component to protect routes for the Farmer role.
 * If the user is logged in AND is a farmer, it renders the FarmerLayout.
 * Otherwise, it navigates them away.
 */
const FarmerRoutes = () => {
  const { user } = useAuth();
  if (!user) return <Navigate to="/login" />;
  if (user.role !== "farmer") return <Navigate to="/" />; // Or a "Not Authorized" page

  return <FarmerLayout />;
};

function App() {
  const { user, loading } = useAuth();

  // Show a loading indicator while the app is checking for an existing token
  if (loading) {
    return <div>Loading...</div>; // Or a proper spinner component
  }

  return (
    <Router>
      <Routes>
        {/* --- Public Routes --- */}
        {/* These routes are accessible only when the user is NOT logged in. */}
        <Route path="/login" element={!user ? <LoginPage /> : <Navigate to="/" />} />
        <Route path="/register" element={!user ? <RegistrationPage /> : <Navigate to="/" />} />

        {/* --- Role-Based Main Route --- */}
        {/* The root path '/' redirects based on the user's role. */}
        <Route
          path="/"
          element={
            !user ? (
              <Navigate to="/login" />
            ) : user.role === "admin" ? (
              <Navigate to="/admin/dashboard" />
            ) : (
              <Navigate to="/farmer/dashboard" />
            )
          }
        />

        {/* --- Admin Protected Routes --- */}
        <Route path="/admin" element={<AdminRoutes />}>
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="services" element={<ServicesPage />} />
          <Route path="experts" element={<ExpertsPage />} />
          <Route path="bookings" element={<BookingsPage />} />
          <Route path="farmers" element={<FarmersPage />} />
        </Route>

        {/* --- Farmer Protected Routes --- */}
        <Route path="/farmer" element={<FarmerRoutes />}>
          <Route path="dashboard" element={<FarmerDashboardPage />} />
          <Route path="browse-services" element={<BrowseServicesPage />} />
          <Route path="my-bookings" element={<FarmerBookingsPage />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;


