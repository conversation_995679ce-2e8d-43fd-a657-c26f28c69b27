// src/components/PaymentModal.jsx

import React, 'useState } from 'react';
import { Modal, Button, Form, Row, Col, Alert } from 'react-bootstrap';

/**
 * A UI component to display a payment form for a booking.
 * IMPORTANT: This is a UI SIMULATION. For a real application, the credit card
 * input fields should be replaced with a secure solution like Stripe Elements
 * to ensure PCI compliance.
 *
 * @param {object} props
 * @param {boolean} props.show - Controls if the modal is visible.
 * @param {function} props.onHide - Function to call when the modal should be closed.
 * @param {object} props.bookingDetails - The details of the booking to be paid for.
 */
const PaymentModal = ({ show, onHide, bookingDetails }) => {
  // State to manage UI feedback
  const [error, setError] = useState('');
  const [processing, setProcessing] = useState(false);

  // If the modal is shown without details, render nothing to prevent errors.
  if (!bookingDetails) {
    return null;
  }

  /**
   * Handles the form submission to simulate payment processing.
   */
  const handlePayment = (event) => {
    event.preventDefault();
    setProcessing(true);
    setError('');

    console.log("Simulating payment processing for:", bookingDetails);
    
    // Simulate a 2-second API call to a payment gateway
    setTimeout(() => {
      console.log("Payment successful!");
      setProcessing(false);
      
      // Provide user feedback and close the modal
      alert('Payment processed successfully! Booking confirmed.');
      onHide();
    }, 2000);
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Payment for {bookingDetails.serviceName}</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {/* Section for Booking Summary */}
        <div className="mb-4">
          <Row>
            <Col><strong>Service:</strong></Col>
            <Col className="text-end">{bookingDetails.serviceName}</Col>
          </Row>
          <Row>
            <Col><strong>Date:</strong></Col>
            <Col className="text-end">{bookingDetails.date}</Col>
          </Row>
          <Row>
            <Col><strong>Time:</strong></Col>
            <Col className="text-end">{bookingDetails.time}</Col>
          </Row>
          <hr />
          <Row className="fw-bold fs-5">
            <Col>Total Amount:</Col>
            <Col className="text-end text-success">${bookingDetails.amount.toFixed(2)}</Col>
          </Row>
        </div>
        
        {/* Display any payment errors here */}
        {error && <Alert variant="danger">{error}</Alert>}

        {/* Payment Form */}
        <Form onSubmit={handlePayment}>
          <Form.Group className="mb-3">
            <Form.Label>Card Number</Form.Label>
            <Form.Control type="text" placeholder="1234 5678 9012 3456" required />
          </Form.Group>

          <Row>
            <Col xs={7}>
              <Form.Group className="mb-3">
                <Form.Label>Expiry Date</Form.Label>
                <Form.Control type="text" placeholder="MM/YY" required />
              </Form.Group>
            </Col>
            <Col xs={5}>
              <Form.Group className="mb-3">
                <Form.Label>CVV</Form.Label>
                <Form.Control type="text" placeholder="123" required />
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3">
            <Form.Label>Cardholder Name</Form.Label>
            <Form.Control type="text" placeholder="Enter name on card" required />
          </Form.Group>

          <div className="d-grid mt-4">
            <Button variant="success" type="submit" disabled={processing}>
              {processing ? 'Processing...' : `Pay $${bookingDetails.amount.toFixed(2)}`}
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default PaymentModal;



