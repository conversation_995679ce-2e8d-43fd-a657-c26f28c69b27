// src/contexts/AuthContext.jsx

import React, { createContext, useState, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import api from "../services/api";
import { loginUser } from "../services/authService";

// 1. Create the context
const AuthContext = createContext();

// 2. Create the provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [accessToken, setAccessToken] = useState(localStorage.getItem("access_token") || null);
  const [refreshToken, setRefreshToken] = useState(localStorage.getItem("refresh_token") || null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // This effect runs on initial app load to check for an existing valid token
    if (accessToken) {
      try {
        const decodedUser = jwtDecode(accessToken);
        const currentTime = Date.now() / 1000;

        if (decodedUser.exp > currentTime) {
          setUser(decodedUser);
          // Set the header for the initial load
          api.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;
        } else {
          // If token is expired, clear it
          setUser(null);
          setAccessToken(null);
          setRefreshToken(null);
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
        }
      } catch (error) {
        // Handle case where token is invalid
        setUser(null);
        setAccessToken(null);
        setRefreshToken(null);
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
      }
    }
    setLoading(false);
  }, []); // Run only once on initial load

  const login = async (username, password) => {
    try {
      const data = await loginUser({ username, password });

      // Store both tokens
      localStorage.setItem("access_token", data.access);
      localStorage.setItem("refresh_token", data.refresh);
      setAccessToken(data.access);
      setRefreshToken(data.refresh);

      // Set default header for future requests
      api.defaults.headers.common["Authorization"] = `Bearer ${data.access}`;

      const decodedUser = jwtDecode(data.access);
      setUser(decodedUser);
    } catch (error) {
      console.error("Login failed:", error);
      // Clear any partial tokens on failed login
      logout();
      throw error;
    }
  };

  const logout = () => {
    // Clear all auth state
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    setAccessToken(null);
    setRefreshToken(null);
    setUser(null);
    delete api.defaults.headers.common["Authorization"];
  };

  const contextData = {
    user,
    accessToken,
    loading,
    login,
    logout,
  };

  // 3. Return the provider, but don't render children until loading is false
  return <AuthContext.Provider value={contextData}>{!loading && children}</AuthContext.Provider>;
};

export default AuthContext;


