// src/contexts/AuthContext.jsx

import React, { createContext, useState, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import api from "../services/api";
import { loginUser } from "../services/authService";

// 1. Create the context
const AuthContext = createContext();

// 2. Create and EXPORT the provider component.
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = () => {
      const token = localStorage.getItem("access_token");

      if (token) {
        try {
          const decodedUser = jwtDecode(token);

          // Check if token is expired
          const currentTime = Date.now() / 1000;
          if (decodedUser.exp > currentTime) {
            setUser(decodedUser);
            api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
          } else {
            // Token expired
            localStorage.removeItem("access_token");
            setUser(null);
            delete api.defaults.headers.common["Authorization"];
          }
        } catch (error) {
          console.error("Invalid token:", error);
          localStorage.removeItem("access_token");
          setUser(null);
          delete api.defaults.headers.common["Authorization"];
        }
      }

      // Always set loading to false after checking
      setLoading(false);
    };

    initializeAuth();
  }, []); // Empty dependency array - only run once

  const login = async (username, password) => {
    try {
      const data = await loginUser({ username, password });
      const newAccessToken = data.access;
      const refreshToken = data.refresh;

      // Update localStorage with both tokens
      localStorage.setItem("access_token", newAccessToken);
      localStorage.setItem("refresh_token", refreshToken);

      // Set authorization header
      api.defaults.headers.common["Authorization"] = `Bearer ${newAccessToken}`;

      // Decode user data
      const decodedUser = jwtDecode(newAccessToken);

      // Update state
      setUser(decodedUser);
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    delete api.defaults.headers.common["Authorization"];
    setUser(null);
  };

  const contextData = {
    user,
    accessToken: localStorage.getItem("access_token"),
    loading,
    login,
    logout,
  };

  return <AuthContext.Provider value={contextData}>{children}</AuthContext.Provider>;
};

// 3. Export the context as the default export.
export default AuthContext;


