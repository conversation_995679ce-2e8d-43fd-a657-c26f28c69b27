// src/pages/DashboardPage.jsx

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import StatCard from "../components/StatCard";
import { getDashboardStats } from "../services/dashboardService";

const DashboardPage = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Use useCallback to create a stable function reference for fetching data.
  // This prevents the useEffect hook from re-running unnecessarily.
  const fetchStats = useCallback(async () => {
    try {
      const data = await getDashboardStats();
      setStats(data);
    } catch (err) {
      setError("Failed to load dashboard data. Please try again later.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, []); // Empty dependency array means this function is created only once.

  // This effect runs once on mount (or if fetchStats ever changes, which it won't).
  useEffect(() => {
    setLoading(true);
    fetchStats();
  }, [fetchStats]);

  const formatCurrency = (number) => {
    // Ensure the input is a valid number before formatting.
    if (typeof number !== "number" && typeof number !== "string") {
      return "$0.00";
    }
    const numericValue = parseFloat(number);
    if (isNaN(numericValue)) {
      return "$0.00";
    }
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(numericValue);
  };

  if (loading) {
    return (
      <Container fluid>
        <div className="mb-4">
          <h2>Dashboard</h2>
          <p className="text-muted">Welcome back, loading your data...</p>
        </div>
        <Row>
          {[1, 2, 3, 4].map((i) => (
            <Col key={i} md={6} lg={3} className="mb-4">
              <div className="card h-100" style={{ background: "linear-gradient(135deg, #1E90FF, #87CEEB)" }}>
                <div className="card-body text-white">
                  <div className="d-flex justify-content-center">
                    <Spinner animation="border" size="sm" />
                  </div>
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  return (
    <Container fluid>
      {/* Only render the content if the stats object exists */}
      {stats && (
        <>
          <Row>
            <Col md={6} lg={3} className="mb-4">
              <StatCard title="Experts" value={stats.total_experts} icon="bi bi-people" />
            </Col>
            <Col md={6} lg={3} className="mb-4">
              <StatCard title="Services" value={stats.total_services} icon="bi bi-grid" />
            </Col>
            <Col md={6} lg={3} className="mb-4">
              <StatCard title="Bookings" value={stats.total_bookings} icon="bi bi-calendar-check" />
            </Col>
            <Col md={6} lg={3} className="mb-4">
              <StatCard title="Revenue" value={formatCurrency(stats.total_revenue)} icon="bi bi-cash-stack" />
            </Col>
          </Row>

          <Row className="mt-4">
            <Col lg={8} className="mb-4">
              <Card>
                <Card.Header>
                  <h4>Recent Bookings</h4>
                </Card.Header>
                <Card.Body>
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Farmer</th>
                          <th>Service</th>
                          <th>Expert</th>
                          <th>Date & Time</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {stats.recent_bookings && stats.recent_bookings.length > 0 ? (
                          stats.recent_bookings.map((booking) => (
                            <tr key={booking.id}>
                              <td>
                                {booking.farmer.first_name} {booking.farmer.last_name}
                              </td>
                              <td>{booking.service.name}</td>
                              <td>
                                {booking.expert.first_name} {booking.expert.last_name}
                              </td>
                              <td>{new Date(booking.booking_time).toLocaleString()}</td>
                              <td>
                                <span className={`badge bg-${booking.status === "confirmed" ? "success" : "warning"}`}>
                                  {booking.status.replace("_", " ").toUpperCase()}
                                </span>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan="5" className="text-center">
                              No recent bookings found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col lg={4} className="mb-4">
              <Card className="text-white" style={{ background: "linear-gradient(45deg, #8A2BE2, #5F9EA0)" }}>
                <Card.Header>
                  <h4>Live Session</h4>
                </Card.Header>
                <Card.Body className="text-center">
                  <i className="bi bi-camera-video-off" style={{ fontSize: "3rem" }}></i>
                  <p className="mt-3">No active live session</p>
                  <Button variant="light" disabled>
                    Start Live Session
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </>
      )}
    </Container>
  );
};

export default DashboardPage;

