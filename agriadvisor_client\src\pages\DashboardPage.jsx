// src/pages/DashboardPage.jsx

import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON>, Button } from "react-bootstrap";
import StatCard from "../components/StatCard";
import PaymentModal from "../components/PaymentModal";
import { getDashboardStats } from "../services/dashboardService";
import useAuth from "../hooks/useAuth";

/**
 * Main dashboard page for the Admin.
 * Displays summary statistics and recent bookings.
 */
const DashboardPage = () => {
  // State for data, loading, and errors
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // State for controlling the payment modal
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);

  const { logout } = useAuth();

  // Fetch all dashboard data on component mount
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const data = await getDashboardStats();
        setStats(data);
      } catch (err) {
        setError("Failed to load dashboard data. Please try again later.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []); // Empty array ensures this runs only once

  /**
   * Prepares booking data and opens the payment modal.
   * @param {object} booking - The full booking object from the API.
   */
  const handleShowPaymentModal = (booking) => {
    const bookingDetails = {
      serviceName: booking.service.name,
      date: new Date(booking.booking_time).toLocaleDateString(),
      time: new Date(booking.booking_time).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      amount: parseFloat(booking.service.price),
    };
    setSelectedBooking(bookingDetails);
    setShowPaymentModal(true);
  };

  // --- Conditional Rendering ---
  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: "80vh" }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  // --- Main Render ---
  return (
    <Container fluid>
      <DashboardHeader onLogout={logout} />

      <StatCardsRow stats={stats} />

      <Row className="mt-4">
        <Col lg={8} className="mb-4">
          <RecentBookingsTable bookings={stats?.recent_bookings} onPayNowClick={handleShowPaymentModal} />
        </Col>
        <Col lg={4} className="mb-4">
          <LiveSessionCard />
        </Col>
      </Row>

      <PaymentModal
        show={showPaymentModal}
        onHide={() => setShowPaymentModal(false)}
        bookingDetails={selectedBooking}
      />
    </Container>
  );
};

// --- Child Components for Readability ---

const DashboardHeader = ({ onLogout }) => (
  <div className="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 className="mb-0">Admin Dashboard</h2>
      <small className="text-muted">Welcome back, Admin!</small>
    </div>
    <Button variant="outline-danger" onClick={onLogout}>
      Logout
    </Button>
  </div>
);

const StatCardsRow = ({ stats }) => {
  const formatCurrency = (number) =>
    new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(number || 0);

  return (
    <Row>
      <Col md={6} lg={3} className="mb-4">
        <StatCard title="Experts" value={stats?.total_experts} icon="bi bi-people" />
      </Col>
      <Col md={6} lg={3} className="mb-4">
        <StatCard title="Services" value={stats?.total_services} icon="bi bi-grid" />
      </Col>
      <Col md={6} lg={3} className="mb-4">
        <StatCard title="Bookings" value={stats?.total_bookings} icon="bi bi-calendar-check" />
      </Col>
      <Col md={6} lg={3} className="mb-4">
        <StatCard title="Revenue" value={formatCurrency(stats?.total_revenue)} icon="bi bi-cash-stack" />
      </Col>
    </Row>
  );
};

const RecentBookingsTable = ({ bookings, onPayNowClick }) => (
  <Card>
    <Card.Header>
      <h4>Recent Bookings</h4>
    </Card.Header>
    <Card.Body>
      <div className="table-responsive">
        <table className="table table-hover">
          <thead>
            <tr>
              <th>Farmer</th>
              <th>Service</th>
              <th>Date & Time</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {bookings && bookings.length > 0 ? (
              bookings.map((booking) => (
                <tr key={booking.id}>
                  <td>
                    {booking.farmer.first_name} {booking.farmer.last_name}
                  </td>
                  <td>{booking.service.name}</td>
                  <td>{new Date(booking.booking_time).toLocaleString()}</td>
                  <td>
                    <span className={`badge bg-${booking.status === "confirmed" ? "success" : "warning text-dark"}`}>
                      {booking.status.replace("_", " ")}
                    </span>
                  </td>
                  <td>
                    {booking.status === "pending_payment" && (
                      <Button variant="outline-success" size="sm" onClick={() => onPayNowClick(booking)}>
                        Pay Now
                      </Button>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="text-center">
                  No recent bookings found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </Card.Body>
  </Card>
);

const LiveSessionCard = () => (
  <Card className="text-white h-100" style={{ background: "linear-gradient(45deg, #8A2BE2, #5F9EA0)" }}>
    <Card.Header>
      <h4>Live Session</h4>
    </Card.Header>
    <Card.Body className="text-center d-flex flex-column justify-content-center align-items-center">
      <i className="bi bi-camera-video-off" style={{ fontSize: "3rem" }}></i>
      <p className="mt-3">No active live session</p>
      <Button variant="light" disabled>
        Start Live Session
      </Button>
    </Card.Body>
  </Card>
);

export default DashboardPage;


