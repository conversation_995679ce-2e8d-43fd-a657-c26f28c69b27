// src/pages/ExpertsPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert } from "react-bootstrap";
import { getExperts, createExpert, updateExpert, deleteExpert } from "../services/expertService";
import ExpertModal from "../components/ExpertModal";
import ExpertCard from "../components/ExpertCard";

const ExpertsPage = () => {
  const [experts, setExperts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingExpert, setEditingExpert] = useState(null);

  const fetchExperts = async () => {
    try {
      setLoading(true);
      setError("");
      const data = await getExperts();
      setExperts(data);
    } catch (err) {
      setError("Failed to fetch experts.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExperts();
  }, []);

  const handleShowAddModal = () => {
    setEditingExpert(null);
    setShowModal(true);
  };

  const handleShowEditModal = (expert) => {
    setEditingExpert(expert);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingExpert(null);
  };

  const handleSaveExpert = async (expertData) => {
    // Format the data to match the backend serializer
    const formattedData = {
      full_name: expertData.full_name,
      email: expertData.email,
      expert_profile: {
        specialty: expertData.specialty,
        bio: expertData.bio,
      },
    };

    try {
      if (editingExpert) {
        await updateExpert(editingExpert.id, formattedData);
      } else {
        await createExpert(formattedData);
      }
      handleCloseModal();
      fetchExperts();
    } catch (err) {
      console.error("Failed to save expert:", err.response.data);
      alert("Failed to save expert. Please check the console for details.");
    }
  };
  

  const handleDeleteExpert = async (id) => {
    if (window.confirm("Are you sure you want to delete this expert?")) {
      try {
        await deleteExpert(id);
        fetchExperts();
      } catch (err) {
        setError("Failed to delete expert. They might be assigned to a booking.");
      }
    }
  };

  if (loading)
    return (
      <Container fluid>
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2>Manage Experts</h2>
          <Button variant="primary" disabled>
            <Spinner animation="border" size="sm" className="me-2" />
            Loading...
          </Button>
        </div>
        <div className="text-center py-5">
          <Spinner animation="border" />
          <div className="mt-2">Loading experts...</div>
        </div>
      </Container>
    );

  return (
    <Container fluid>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>Manage Experts</h2>
        <Button variant="primary" onClick={handleShowAddModal}>
          <i className="bi bi-plus-lg me-2"></i>Add New Expert
        </Button>
      </div>

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      <Row>
        {experts.length > 0
          ? experts.map((expert) => (
              <Col key={expert.id} md={6} lg={4} xl={3} className="mb-4">
                <ExpertCard expert={expert} onEdit={handleShowEditModal} onDelete={handleDeleteExpert} />
              </Col>
            ))
          : !loading && (
              <Col>
                <div className="text-center p-5 bg-light rounded">
                  <h4>No experts found.</h4>
                  <p>Add one to get started!</p>
                </div>
              </Col>
            )}
      </Row>

      <ExpertModal show={showModal} onHide={handleCloseModal} onSave={handleSaveExpert} expert={editingExpert} />
    </Container>
  );
};

export default ExpertsPage;




