// src/pages/RegistrationPage.jsx

import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { Container, Form, But<PERSON>, Card, Alert } from "react-bootstrap";
import { registerUser } from "../services/authService";

const RegistrationPage = () => {
  const [formData, setFormData] = useState({
    tenant_name: "",
    first_name: "",
    last_name: "",
    username: "",
    email: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    // Our backend serializer combines first_name and last_name into full_name
    // and tenant_name is the name of their farm/business.
    const registrationData = {
      tenant_name: formData.tenant_name,
      first_name: formData.first_name,
      last_name: formData.last_name,
      username: formData.username,
      email: formData.email,
      password: formData.password,
    };

    try {
      await registerUser(registrationData);
      setSuccess("Registration successful! Please log in.");
      // Redirect to the login page after a short delay
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (err) {
      // Handle potential validation errors from the backend
      let errorMessage = "Registration failed. Please try again.";
      if (err.response && err.response.data) {
        const errors = err.response.data;
        const errorMessages = Object.keys(errors).map((key) => {
          return `${key}: ${errors[key].join(", ")}`;
        });
        errorMessage = errorMessages.join(" | ");
      }
      setError(errorMessage);
      console.error(err);
    }
  };

  return (
    <Container
      className="d-flex align-items-center justify-content-center"
      style={{ minHeight: "100vh", backgroundColor: "#f8f9fa" }}
    >
      <Card style={{ width: "500px" }}>
        <Card.Body>
          <h2 className="text-center mb-4">Register as a Farmer</h2>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Farm or Business Name</Form.Label>
              <Form.Control type="text" name="tenant_name" onChange={handleChange} required />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>First Name</Form.Label>
              <Form.Control type="text" name="first_name" onChange={handleChange} required />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Last Name</Form.Label>
              <Form.Control type="text" name="last_name" onChange={handleChange} required />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Username</Form.Label>
              <Form.Control type="text" name="username" onChange={handleChange} required />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Email</Form.Label>
              <Form.Control type="email" name="email" onChange={handleChange} required />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Password</Form.Label>
              <Form.Control type="password" name="password" onChange={handleChange} required />
            </Form.Group>

            <Button variant="primary" type="submit" className="w-100">
              Register
            </Button>
          </Form>
          <div className="text-center mt-3">
            Already have an account? <Link to="/login">Log In</Link>
          </div>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default RegistrationPage;

