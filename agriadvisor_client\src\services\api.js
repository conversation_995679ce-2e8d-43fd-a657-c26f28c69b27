// src/services/api.js

import axios from "axios";

const api = axios.create({
  baseURL: "http://localhost:8001/api",
});

// --- THIS IS THE REQUEST INTERCEPTOR ---
// It runs BEFORE every request is sent.
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("access_token");
    if (token) {
      config.headers["Authorization"] = `<PERSON><PERSON> ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// --- THIS IS THE RESPONSE INTERCEPTOR ---
// It runs AFTER a response is received, but ONLY if there was an error.
api.interceptors.response.use(
  // If the response is successful, do nothing and just return it.
  (response) => {
    return response;
  },
  // If the response has an error...
  async (error) => {
    const originalRequest = error.config;

    // Check if the error is 401 and we haven't tried to refresh yet.
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem("refresh_token");
        if (!refreshToken) {
          // If no refresh token is found, redirect to login
          window.location.href = "/login";
          return Promise.reject(error);
        }

        // Make the call to the refresh endpoint
        const response = await axios.post("http://localhost:8001/api/accounts/token/refresh/", {
          refresh: refreshToken,
        });

        const newAccessToken = response.data.access;

        // Save the new token
        localStorage.setItem("access_token", newAccessToken);

        // Update the default auth header for all future requests
        api.defaults.headers.common["Authorization"] = `Bearer ${newAccessToken}`;

        // Update the header on the original request that failed
        originalRequest.headers["Authorization"] = `Bearer ${newAccessToken}`;

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);
        // If the refresh fails, clear storage and redirect to login
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
        window.location.href = "/login";
        return Promise.reject(refreshError);
      }
    }

    // For any other errors, just pass them along
    return Promise.reject(error);
  }
);

export default api;



