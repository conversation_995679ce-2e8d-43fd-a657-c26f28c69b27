// src/services/api.js

import axios from "axios";

const api = axios.create({
  baseURL: "http://localhost:8001/api",
});

// --- THIS IS THE REQUEST INTERCEPTOR ---
// It runs BEFORE every request is sent.
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("access_token");
    if (token) {
      config.headers["Authorization"] = `<PERSON><PERSON> ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// --- NEW: The Response Interceptor ---
api.interceptors.response.use(
  // If the response is successful (2xx), just return it
  (response) => {
    return response;
  },
  // If the response has an error
  async (error) => {
    const originalRequest = error.config;

    // Check if the error is a 401 and if we haven't already retried this request
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true; // Mark that we've retried this request

      try {
        // Get the refresh token from localStorage
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
            // If no refresh token, logout the user (or redirect to login)
            // This logic can be enhanced in AuthContext
            window.location.href = '/login'; 
            return Promise.reject(error);
        }

        // Make the call to the refresh token endpoint
        const response = await axios.post('http://localhost:8001/api/accounts/token/refresh/', {
          refresh: refreshToken,
        });

        const newAccessToken = response.data.access;

        // Save the new access token
        localStorage.setItem('access_token', newAccessToken);

        // Update the default Authorization header for all future requests
        api.defaults.headers.common['Authorization'] = `Bearer ${newAccessToken}`;

        // Update the header of the original request that failed
        originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;

        // Retry the original request with the new token
        return api(originalRequest);

      } catch (refreshError) {
        // If the refresh token is also invalid, logout the user
        console.error("Refresh token is invalid", refreshError);
        // This logic can be enhanced in AuthContext
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // For any other errors, just reject the promise
    return Promise.reject(error);
  }
);


export default api;


